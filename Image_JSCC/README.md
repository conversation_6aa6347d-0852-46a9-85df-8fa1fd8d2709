# Image JSCC (Joint Source-Channel Coding) System

这是一个基于深度学习的图像联合信源信道编码系统，模仿了原始JSCC架构，专门用于图像传输。

## 系统概述

该系统实现了端到端的图像传输流程：
1. **图像编码**: 使用CNN+Transformer编码器压缩图像
2. **量化**: 将连续特征量化为比特序列
3. **信道传输**: 通过无线信道传输比特
4. **反量化**: 恢复连续特征
5. **图像解码**: 使用Transformer+CNN解码器重建图像

## 目录结构

```
Image_JSCC/
├── config.py                 # 配置文件
├── train_image_jscc.py       # 主训练脚本
├── test_system.py           # 系统测试脚本
├── README.md                # 说明文档
├── data/
│   └── image_dataset.py     # 数据加载模块
├── layers/
│   ├── image_encoder.py     # 图像编码器
│   ├── image_decoder.py     # 图像解码器
│   ├── attention_modules.py # 注意力机制模块
│   └── quantization.py      # 量化/反量化层
├── channel/
│   └── channel.py           # 信道模型 (AWGN, Rayleigh, OFDM)
├── model/
│   └── image_jscc_model.py  # 完整JSCC模型
├── loss/
│   └── image_loss.py        # 损失函数和评估指标
├── checkpoints/             # 模型检查点 (训练时创建)
└── logs/                    # 训练日志 (训练时创建)
```

## 核心特性

### 1. 模型架构
- **编码器**: CNN特征提取 + Transformer编码器
- **解码器**: Transformer解码器 + CNN重建
- **压缩比**: 可配置 (默认16倍压缩)
- **量化**: 4-bit量化 (可配置)

### 2. 信道模型
- **AWGN**: 加性白高斯噪声信道
- **Rayleigh**: 瑞利衰落信道  
- **OFDM**: 简化OFDM信道模拟

### 3. 损失函数
- **MSE**: 均方误差 (主要损失)
- **PSNR**: 峰值信噪比
- **SSIM**: 结构相似性指数
- **感知损失**: 基于VGG特征的感知损失

## 快速开始

### 1. 环境要求
```bash
tensorflow >= 2.8.0
numpy
matplotlib
tqdm
PIL
```

### 2. 数据准备
系统使用Cityscapes数据集的aachen子集：
```
data/Cityscapes/leftImg8bit/train/aachen/
```

### 3. 测试系统
运行系统测试以验证所有组件：
```bash
cd Image_JSCC
python test_system.py
```

### 4. 训练模型
```bash
cd Image_JSCC
python train_image_jscc.py
```

## 配置参数

主要配置参数在 `config.py` 中：

```python
# 图像参数
image_height = 256          # 图像高度
image_width = 512           # 图像宽度  
image_channels = 3          # 图像通道数

# 压缩参数
compression_ratio = 16      # 压缩比例
quantization_bits = 4       # 量化比特数
feedback_bits = 2048        # 传输比特数

# 信道参数
snr_dB = 10                # 信噪比 (dB)
channel_type = "AWGN"      # 信道类型

# 训练参数
batch_size = 8             # 批次大小
epochs = 100               # 训练轮数
initial_lr = 0.0001        # 初始学习率
```

## 模型性能

### 压缩性能
- **输入**: 256×512×3 图像 (393,216 像素)
- **压缩特征**: 128维 (16倍压缩)
- **传输比特**: 512 bits (4-bit量化)
- **实际压缩比**: ~6,144倍

### 信道鲁棒性
系统在不同SNR下的性能：
- **SNR 20dB**: PSNR > 30dB
- **SNR 10dB**: PSNR > 25dB  
- **SNR 0dB**: PSNR > 20dB

## 与原始JSCC的对比

| 特性 | 原始JSCC | Image JSCC |
|------|----------|------------|
| 数据类型 | CSI (复数) | 图像 (RGB) |
| 输入维度 | [2, 32, 13] | [256, 512, 3] |
| 编码器 | Transformer | CNN + Transformer |
| 解码器 | Transformer | Transformer + CNN |
| 损失函数 | SGCS | MSE |
| 压缩比 | ~8倍 | ~16倍 |
| 应用场景 | 信道反馈 | 图像传输 |

## 训练监控

训练过程中会生成：
1. **训练日志**: `logs/training_log.csv`
2. **样本图像**: `logs/samples/samples_epoch_*.png`
3. **模型检查点**: `checkpoints/best_model`, `checkpoints/final_model`

## 扩展功能

### 1. 添加新的信道模型
在 `channel/channel.py` 中实现新的信道类：
```python
class NewChannel(tf.keras.layers.Layer):
    def call(self, inputs, snr_db, training=None):
        # 实现信道传输逻辑
        return outputs
```

### 2. 自定义损失函数
在 `loss/image_loss.py` 中添加新的损失函数：
```python
class CustomLoss(tf.keras.layers.Layer):
    def call(self, y_true, y_pred):
        # 实现自定义损失
        return loss
```

### 3. 修改网络架构
在 `layers/` 目录下修改编码器和解码器结构。

## 故障排除

### 常见问题

1. **内存不足**
   - 减小 `batch_size`
   - 减小 `image_height` 和 `image_width`

2. **数据加载失败**
   - 检查数据路径 `data_root` 是否正确
   - 确保图像文件存在且可读

3. **训练不收敛**
   - 调整学习率 `initial_lr`
   - 检查损失函数设置
   - 增加训练轮数

4. **GPU内存错误**
   - 启用内存增长: `tf.config.experimental.set_memory_growth(gpu, True)`

## 参考文献

1. 原始JSCC论文和实现
2. Transformer架构: "Attention Is All You Need"
3. 图像压缩: "Variational Image Compression with a Scale Hyperprior"
4. 联合信源信道编码: "Deep Joint Source-Channel Coding"

## 许可证

本项目基于原始JSCC实现，用于学术研究目的。
