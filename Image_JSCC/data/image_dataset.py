import tensorflow as tf
import os
import glob
from PIL import Image
import numpy as np

class CityscapesDataset:
    def __init__(self, config):
        self.config = config
        self.image_height = config.image_height
        self.image_width = config.image_width
        self.image_channels = config.image_channels
        
    def load_image_paths(self, data_root):
        """加载图像路径列表"""
        if os.path.isdir(data_root):
            # 如果是目录，获取所有PNG文件
            image_paths = glob.glob(os.path.join(data_root, "*.png"))
        else:
            # 如果是单个文件路径
            image_paths = [data_root] if os.path.exists(data_root) else []
        
        print(f"Found {len(image_paths)} images in {data_root}")
        return sorted(image_paths)
    
    def preprocess_image(self, image_path):
        """预处理单张图像"""
        # 读取图像
        image = tf.io.read_file(image_path)
        image = tf.image.decode_png(image, channels=self.image_channels)
        
        # 调整大小
        image = tf.image.resize(image, [self.image_height, self.image_width])
        
        # 归一化到 [0, 1]
        image = tf.cast(image, tf.float32) / 255.0
        
        return image
    
    def create_dataset(self, data_root, batch_size, shuffle=True, repeat=True):
        """创建TensorFlow数据集"""
        image_paths = self.load_image_paths(data_root)
        
        if not image_paths:
            raise ValueError(f"No images found in {data_root}")
        
        # 创建路径数据集
        path_dataset = tf.data.Dataset.from_tensor_slices(image_paths)
        
        # 映射预处理函数
        dataset = path_dataset.map(
            self.preprocess_image,
            num_parallel_calls=tf.data.AUTOTUNE
        )
        
        if shuffle:
            dataset = dataset.shuffle(buffer_size=min(1000, len(image_paths)))
        
        if repeat:
            dataset = dataset.repeat()
            
        dataset = dataset.batch(batch_size)
        dataset = dataset.prefetch(tf.data.AUTOTUNE)
        
        return dataset
    
    def get_train_dataset(self, batch_size, shuffle=True):
        """获取训练数据集"""
        return self.create_dataset(
            self.config.data_root, 
            batch_size, 
            shuffle=shuffle, 
            repeat=True
        )
    
    def get_val_dataset(self, batch_size, shuffle=False):
        """获取验证数据集"""
        # 使用部分训练数据作为验证集，或者使用验证目录
        val_root = getattr(self.config, 'val_data_root', self.config.data_root)
        return self.create_dataset(
            val_root, 
            batch_size, 
            shuffle=shuffle, 
            repeat=False
        )

def get_image_dataset(config, batch_size, dataset_type='train'):
    """获取图像数据集的便捷函数"""
    dataset_loader = CityscapesDataset(config)
    
    if dataset_type == 'train':
        return dataset_loader.get_train_dataset(batch_size, shuffle=True)
    elif dataset_type == 'val':
        return dataset_loader.get_val_dataset(batch_size, shuffle=False)
    else:
        raise ValueError(f"Unknown dataset_type: {dataset_type}")

# 测试函数
def test_dataset(config):
    """测试数据集加载"""
    dataset = get_image_dataset(config, batch_size=2, dataset_type='train')
    
    for batch in dataset.take(1):
        print(f"Batch shape: {batch.shape}")
        print(f"Data type: {batch.dtype}")
        print(f"Value range: [{tf.reduce_min(batch):.3f}, {tf.reduce_max(batch):.3f}]")
        break

if __name__ == "__main__":
    from config import ImageJSCCConfig
    config = ImageJSCCConfig()
    test_dataset(config)
