#!/usr/bin/env python3
"""
Image JSCC系统使用示例
演示如何使用该系统进行图像传输
"""

import os
import sys
import tensorflow as tf

# 设置环境
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

def run_system_test():
    """运行系统测试"""
    print("🚀 Running Image JSCC System Test...")
    
    try:
        from test_system import run_all_tests
        success = run_all_tests()
        
        if success:
            print("\n✅ System test completed successfully!")
            return True
        else:
            print("\n❌ System test failed. Please check the errors above.")
            return False
            
    except Exception as e:
        print(f"❌ Failed to run system test: {e}")
        return False

def run_training_demo():
    """运行训练演示"""
    print("\n🎯 Running Training Demo...")
    
    try:
        # 修改配置以进行快速演示
        from config import ImageJSCCConfig
        config = ImageJSCCConfig()
        
        # 设置较小的参数用于演示
        config.epochs = 5  # 只训练5个epoch
        config.batch_size = 4  # 较小的batch size
        config.test_interval = 2  # 更频繁的测试
        
        print(f"Demo configuration:")
        print(f"  Epochs: {config.epochs}")
        print(f"  Batch size: {config.batch_size}")
        print(f"  Image size: {config.image_height}x{config.image_width}")
        
        # 检查数据是否存在
        if not os.path.exists(config.data_root):
            print(f"⚠️  Data directory not found: {config.data_root}")
            print("Please ensure Cityscapes data is available or modify the data_root in config.py")
            return False
        
        # 运行训练
        print("Starting training demo...")
        from train_image_jscc import main as train_main
        
        # 临时修改配置
        original_epochs = config.epochs
        original_batch_size = config.batch_size
        original_test_interval = config.test_interval
        
        try:
            train_main()
            print("✅ Training demo completed!")
            return True
        finally:
            # 恢复原始配置
            config.epochs = original_epochs
            config.batch_size = original_batch_size
            config.test_interval = original_test_interval
            
    except Exception as e:
        print(f"❌ Training demo failed: {e}")
        return False

def run_inference_demo():
    """运行推理演示"""
    print("\n🔍 Running Inference Demo...")
    
    try:
        from config import ImageJSCCConfig
        from model.image_jscc_model import create_model
        import numpy as np
        import matplotlib.pyplot as plt
        
        config = ImageJSCCConfig()
        
        # 创建模型 (使用随机权重进行演示)
        model = create_model(config)
        print("Model created successfully!")
        
        # 创建随机测试图像
        test_image = tf.random.uniform([1, config.image_height, config.image_width, config.image_channels])
        print(f"Test image shape: {test_image.shape}")
        
        # 测试不同SNR
        snr_values = [0, 5, 10, 15, 20]
        results = []
        
        print("Testing different SNR values...")
        for snr in snr_values:
            # 前向传播
            reconstructed = model(test_image, snr_db=snr, training=False)
            
            # 计算PSNR
            psnr = tf.image.psnr(test_image, reconstructed, max_val=1.0)
            psnr_mean = tf.reduce_mean(psnr)
            
            results.append((snr, psnr_mean.numpy()))
            print(f"  SNR {snr:2d} dB: PSNR = {psnr_mean:.2f} dB")
        
        # 保存演示结果
        demo_dir = "Image_JSCC/demo_results"
        os.makedirs(demo_dir, exist_ok=True)
        
        # 可视化结果
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(15, 5))
        
        # 原始图像
        ax1.imshow(test_image[0].numpy())
        ax1.set_title('Original Image')
        ax1.axis('off')
        
        # 重建图像 (SNR=10dB)
        reconstructed_demo = model(test_image, snr_db=10, training=False)
        ax2.imshow(reconstructed_demo[0].numpy())
        ax2.set_title('Reconstructed (SNR=10dB)')
        ax2.axis('off')
        
        # 性能曲线
        snrs, psnrs = zip(*results)
        ax3.plot(snrs, psnrs, 'b-o', linewidth=2, markersize=8)
        ax3.set_xlabel('SNR (dB)')
        ax3.set_ylabel('PSNR (dB)')
        ax3.set_title('PSNR vs SNR')
        ax3.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(demo_dir, 'inference_demo.png'), dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"✅ Inference demo completed! Results saved to {demo_dir}/")
        return True
        
    except Exception as e:
        print(f"❌ Inference demo failed: {e}")
        return False

def show_system_info():
    """显示系统信息"""
    print("📋 Image JSCC System Information")
    print("=" * 50)
    
    try:
        from config import ImageJSCCConfig
        config = ImageJSCCConfig()
        
        print(f"Configuration:")
        print(f"  Image size: {config.image_height} × {config.image_width} × {config.image_channels}")
        print(f"  Compression ratio: {config.compression_ratio}")
        print(f"  Quantization bits: {config.quantization_bits}")
        print(f"  Channel type: {config.channel_type}")
        print(f"  SNR: {config.snr_dB} dB")
        print(f"  Batch size: {config.batch_size}")
        print(f"  Epochs: {config.epochs}")
        
        print(f"\nPaths:")
        print(f"  Data root: {config.data_root}")
        print(f"  Checkpoint dir: {config.ckpt_dir}")
        print(f"  Log dir: {config.log_dir}")
        
        # 检查数据
        data_exists = os.path.exists(config.data_root)
        print(f"\nData availability:")
        print(f"  Data directory exists: {'✅' if data_exists else '❌'}")
        
        if data_exists:
            import glob
            image_files = glob.glob(os.path.join(config.data_root, "*.png"))
            print(f"  Number of images: {len(image_files)}")
        
        # 检查GPU
        gpus = tf.config.list_physical_devices('GPU')
        print(f"\nHardware:")
        print(f"  GPUs available: {len(gpus)}")
        if gpus:
            for i, gpu in enumerate(gpus):
                print(f"    GPU {i}: {gpu.name}")
        
        print(f"  TensorFlow version: {tf.__version__}")
        
    except Exception as e:
        print(f"❌ Failed to get system info: {e}")

def main():
    """主函数"""
    print("🎨 Image JSCC System - Usage Example")
    print("=" * 60)
    
    # 显示系统信息
    show_system_info()
    
    # 运行演示
    demos = [
        ("System Test", run_system_test),
        ("Inference Demo", run_inference_demo),
    ]
    
    success_count = 0
    
    for demo_name, demo_func in demos:
        print(f"\n{'='*20} {demo_name} {'='*20}")
        try:
            success = demo_func()
            if success:
                success_count += 1
        except KeyboardInterrupt:
            print("\n⏹️  Demo interrupted by user")
            break
        except Exception as e:
            print(f"❌ {demo_name} failed: {e}")
    
    # 询问是否运行训练演示
    print(f"\n{'='*20} Training Demo {'='*20}")
    print("⚠️  Training demo will take longer and requires data.")
    
    try:
        response = input("Do you want to run the training demo? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            if run_training_demo():
                success_count += 1
        else:
            print("Skipping training demo.")
    except KeyboardInterrupt:
        print("\nSkipping training demo.")
    
    # 总结
    print(f"\n{'='*60}")
    print(f"Demo Summary: {success_count} demos completed successfully")
    
    if success_count >= 2:
        print("🎉 System is working correctly!")
        print("\nNext steps:")
        print("1. Prepare your image data")
        print("2. Adjust configuration in config.py")
        print("3. Run: python train_image_jscc.py")
        print("4. Use: python inference.py --image your_image.jpg")
    else:
        print("⚠️  Some issues detected. Please check the error messages above.")
    
    print("\nFor more information, see README.md")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
