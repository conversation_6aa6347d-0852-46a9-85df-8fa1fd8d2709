import tensorflow as tf
from tensorflow.keras import losses
import numpy as np

class MSELoss(tf.keras.layers.Layer):
    """均方误差损失"""
    
    def __init__(self, reduction='mean', **kwargs):
        super(MSELoss, self).__init__(**kwargs)
        self.reduction = reduction
        self.mse_fn = losses.MeanSquaredError(reduction=losses.Reduction.NONE)
    
    def call(self, y_true, y_pred):
        """
        计算MSE损失
        
        Args:
            y_true: [batch, height, width, channels] 真实图像
            y_pred: [batch, height, width, channels] 预测图像
        
        Returns:
            loss: MSE损失值
        """
        # 展平图像
        y_true_flat = tf.reshape(y_true, [tf.shape(y_true)[0], -1])
        y_pred_flat = tf.reshape(y_pred, [tf.shape(y_pred)[0], -1])
        
        # 计算每个样本的MSE
        mse_per_sample = self.mse_fn(y_true_flat, y_pred_flat)
        
        if self.reduction == 'none':
            return mse_per_sample
        elif self.reduction == 'mean':
            return tf.reduce_mean(mse_per_sample)
        elif self.reduction == 'sum':
            return tf.reduce_sum(mse_per_sample)
        else:
            raise ValueError(f"Unknown reduction: {self.reduction}")

class PSNRLoss(tf.keras.layers.Layer):
    """PSNR损失 (负PSNR，用于最小化)"""
    
    def __init__(self, max_val=1.0, **kwargs):
        super(PSNRLoss, self).__init__(**kwargs)
        self.max_val = max_val
    
    def call(self, y_true, y_pred):
        """
        计算PSNR损失
        
        Args:
            y_true: [batch, height, width, channels] 真实图像
            y_pred: [batch, height, width, channels] 预测图像
        
        Returns:
            loss: 负PSNR值 (用于最小化)
        """
        psnr = tf.image.psnr(y_true, y_pred, max_val=self.max_val)
        return -tf.reduce_mean(psnr)  # 负号用于最小化

class SSIMLoss(tf.keras.layers.Layer):
    """SSIM损失 (1 - SSIM，用于最小化)"""
    
    def __init__(self, max_val=1.0, **kwargs):
        super(SSIMLoss, self).__init__(**kwargs)
        self.max_val = max_val
    
    def call(self, y_true, y_pred):
        """
        计算SSIM损失
        
        Args:
            y_true: [batch, height, width, channels] 真实图像
            y_pred: [batch, height, width, channels] 预测图像
        
        Returns:
            loss: 1 - SSIM值 (用于最小化)
        """
        ssim = tf.image.ssim(y_true, y_pred, max_val=self.max_val)
        return 1.0 - tf.reduce_mean(ssim)

class PerceptualLoss(tf.keras.layers.Layer):
    """感知损失 (基于VGG特征)"""
    
    def __init__(self, feature_layers=['block3_conv3', 'block4_conv3'], **kwargs):
        super(PerceptualLoss, self).__init__(**kwargs)
        self.feature_layers = feature_layers
        self.vgg = None
        self.feature_extractor = None
        
    def build(self, input_shape):
        """构建VGG特征提取器"""
        # 加载预训练的VGG19模型
        vgg = tf.keras.applications.VGG19(
            include_top=False,
            weights='imagenet',
            input_shape=input_shape[1:]  # 去掉batch维度
        )
        vgg.trainable = False
        
        # 创建特征提取器
        outputs = [vgg.get_layer(name).output for name in self.feature_layers]
        self.feature_extractor = tf.keras.Model(vgg.input, outputs)
        
        super().build(input_shape)
    
    def call(self, y_true, y_pred):
        """
        计算感知损失
        
        Args:
            y_true: [batch, height, width, channels] 真实图像
            y_pred: [batch, height, width, channels] 预测图像
        
        Returns:
            loss: 感知损失值
        """
        # 预处理图像 (VGG需要RGB格式，值范围[0,255])
        y_true_vgg = y_true * 255.0
        y_pred_vgg = y_pred * 255.0
        
        # 如果是灰度图像，复制到3个通道
        if tf.shape(y_true)[-1] == 1:
            y_true_vgg = tf.repeat(y_true_vgg, 3, axis=-1)
            y_pred_vgg = tf.repeat(y_pred_vgg, 3, axis=-1)
        
        # VGG预处理
        y_true_vgg = tf.keras.applications.vgg19.preprocess_input(y_true_vgg)
        y_pred_vgg = tf.keras.applications.vgg19.preprocess_input(y_pred_vgg)
        
        # 提取特征
        features_true = self.feature_extractor(y_true_vgg)
        features_pred = self.feature_extractor(y_pred_vgg)
        
        # 计算特征损失
        total_loss = 0.0
        for feat_true, feat_pred in zip(features_true, features_pred):
            loss = tf.reduce_mean(tf.square(feat_true - feat_pred))
            total_loss += loss
        
        return total_loss / len(self.feature_layers)

class CombinedLoss(tf.keras.layers.Layer):
    """组合损失函数"""
    
    def __init__(self, 
                 mse_weight=1.0,
                 ssim_weight=0.1,
                 perceptual_weight=0.01,
                 **kwargs):
        super(CombinedLoss, self).__init__(**kwargs)
        self.mse_weight = mse_weight
        self.ssim_weight = ssim_weight
        self.perceptual_weight = perceptual_weight
        
        # 损失函数组件
        self.mse_loss = MSELoss()
        self.ssim_loss = SSIMLoss()
        if perceptual_weight > 0:
            self.perceptual_loss = PerceptualLoss()
        else:
            self.perceptual_loss = None
    
    def call(self, y_true, y_pred):
        """
        计算组合损失
        
        Args:
            y_true: [batch, height, width, channels] 真实图像
            y_pred: [batch, height, width, channels] 预测图像
        
        Returns:
            loss: 组合损失值
        """
        total_loss = 0.0
        
        # MSE损失
        if self.mse_weight > 0:
            mse = self.mse_loss(y_true, y_pred)
            total_loss += self.mse_weight * mse
        
        # SSIM损失
        if self.ssim_weight > 0:
            ssim = self.ssim_loss(y_true, y_pred)
            total_loss += self.ssim_weight * ssim
        
        # 感知损失
        if self.perceptual_weight > 0 and self.perceptual_loss is not None:
            perceptual = self.perceptual_loss(y_true, y_pred)
            total_loss += self.perceptual_weight * perceptual
        
        return total_loss

class ImageMetrics(tf.keras.layers.Layer):
    """图像质量评估指标"""
    
    def __init__(self, max_val=1.0, **kwargs):
        super(ImageMetrics, self).__init__(**kwargs)
        self.max_val = max_val
        self.mse_loss = MSELoss(reduction='mean')
    
    def call(self, y_true, y_pred):
        """
        计算各种图像质量指标
        
        Args:
            y_true: [batch, height, width, channels] 真实图像
            y_pred: [batch, height, width, channels] 预测图像
        
        Returns:
            metrics: 包含各种指标的字典
        """
        # MSE
        mse = self.mse_loss(y_true, y_pred)
        
        # PSNR
        psnr = tf.image.psnr(y_true, y_pred, max_val=self.max_val)
        psnr_mean = tf.reduce_mean(psnr)
        
        # SSIM
        ssim = tf.image.ssim(y_true, y_pred, max_val=self.max_val)
        ssim_mean = tf.reduce_mean(ssim)
        
        # MAE
        mae = tf.reduce_mean(tf.abs(y_true - y_pred))
        
        return {
            'mse': mse,
            'psnr': psnr_mean,
            'ssim': ssim_mean,
            'mae': mae
        }

# 便捷函数
def create_loss_function(loss_type='mse', **kwargs):
    """创建损失函数的工厂函数"""
    if loss_type.lower() == 'mse':
        return MSELoss(**kwargs)
    elif loss_type.lower() == 'psnr':
        return PSNRLoss(**kwargs)
    elif loss_type.lower() == 'ssim':
        return SSIMLoss(**kwargs)
    elif loss_type.lower() == 'perceptual':
        return PerceptualLoss(**kwargs)
    elif loss_type.lower() == 'combined':
        return CombinedLoss(**kwargs)
    else:
        raise ValueError(f"Unknown loss type: {loss_type}")

# 测试函数
def test_losses():
    """测试损失函数"""
    batch_size = 2
    height, width, channels = 256, 512, 3
    
    # 创建测试数据
    y_true = tf.random.uniform([batch_size, height, width, channels], 0.0, 1.0)
    y_pred = tf.random.uniform([batch_size, height, width, channels], 0.0, 1.0)
    
    print(f"Test data shape: {y_true.shape}")
    
    # 测试各种损失函数
    losses_to_test = ['mse', 'psnr', 'ssim']
    
    for loss_name in losses_to_test:
        loss_fn = create_loss_function(loss_name)
        loss_value = loss_fn(y_true, y_pred)
        print(f"{loss_name.upper()} loss: {loss_value:.4f}")
    
    # 测试指标
    metrics = ImageMetrics()
    metric_values = metrics(y_true, y_pred)
    print("\nMetrics:")
    for name, value in metric_values.items():
        print(f"{name.upper()}: {value:.4f}")

if __name__ == "__main__":
    test_losses()
