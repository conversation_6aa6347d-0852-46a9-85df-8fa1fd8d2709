import tensorflow as tf
import numpy as np

class AWGNChannel(tf.keras.layers.Layer):
    """加性白高斯噪声信道"""
    
    def __init__(self, **kwargs):
        super(AWGNChannel, self).__init__(**kwargs)
    
    def call(self, inputs, snr_db, training=None):
        """
        通过AWGN信道传输信号
        
        Args:
            inputs: [batch, features] float32, 输入信号 (比特序列)
            snr_db: float, 信噪比 (dB)
            training: bool, 是否为训练模式
        
        Returns:
            output: [batch, features] float32, 信道输出
        """
        # 将比特序列映射到 [-1, 1] (BPSK调制)
        modulated = 2.0 * inputs - 1.0
        
        if training or True:  # 在训练和测试时都添加噪声
            # 计算噪声功率
            snr_linear = tf.pow(10.0, snr_db / 10.0)
            signal_power = tf.reduce_mean(tf.square(modulated))
            noise_power = signal_power / snr_linear
            noise_std = tf.sqrt(noise_power)
            
            # 添加高斯噪声
            noise = tf.random.normal(tf.shape(modulated), stddev=noise_std)
            received = modulated + noise
        else:
            received = modulated
        
        # 解调回 [0, 1]
        demodulated = (received + 1.0) / 2.0
        demodulated = tf.clip_by_value(demodulated, 0.0, 1.0)
        
        return demodulated

class RayleighChannel(tf.keras.layers.Layer):
    """瑞利衰落信道"""
    
    def __init__(self, **kwargs):
        super(RayleighChannel, self).__init__(**kwargs)
    
    def call(self, inputs, snr_db, training=None):
        """
        通过瑞利衰落信道传输信号
        
        Args:
            inputs: [batch, features] float32, 输入信号
            snr_db: float, 信噪比 (dB)
            training: bool, 是否为训练模式
        
        Returns:
            output: [batch, features] float32, 信道输出
        """
        # BPSK调制
        modulated = 2.0 * inputs - 1.0
        
        if training or True:
            # 瑞利衰落系数 (复数)
            h_real = tf.random.normal(tf.shape(modulated), stddev=1.0/tf.sqrt(2.0))
            h_imag = tf.random.normal(tf.shape(modulated), stddev=1.0/tf.sqrt(2.0))
            h_magnitude = tf.sqrt(h_real**2 + h_imag**2)
            
            # 信道衰落
            faded = modulated * h_magnitude
            
            # 添加噪声
            snr_linear = tf.pow(10.0, snr_db / 10.0)
            signal_power = tf.reduce_mean(tf.square(faded))
            noise_power = signal_power / snr_linear
            noise_std = tf.sqrt(noise_power)
            
            noise = tf.random.normal(tf.shape(faded), stddev=noise_std)
            received = faded + noise
            
            # 理想信道估计和均衡
            equalized = received / (h_magnitude + 1e-8)
        else:
            equalized = modulated
        
        # 解调
        demodulated = (equalized + 1.0) / 2.0
        demodulated = tf.clip_by_value(demodulated, 0.0, 1.0)
        
        return demodulated

class OFDMChannel(tf.keras.layers.Layer):
    """简化的OFDM信道模拟"""
    
    def __init__(self, num_subcarriers=64, cp_length=16, **kwargs):
        super(OFDMChannel, self).__init__(**kwargs)
        self.num_subcarriers = num_subcarriers
        self.cp_length = cp_length
    
    def call(self, inputs, snr_db, training=None):
        """
        通过OFDM信道传输信号
        
        Args:
            inputs: [batch, features] float32, 输入比特序列
            snr_db: float, 信噪比 (dB)
            training: bool, 是否为训练模式
        
        Returns:
            output: [batch, features] float32, 信道输出
        """
        batch_size = tf.shape(inputs)[0]
        num_features = tf.shape(inputs)[1]
        
        # 将比特序列重塑为OFDM符号
        # 计算需要的OFDM符号数
        bits_per_symbol = self.num_subcarriers * 2  # QPSK: 2 bits per subcarrier
        num_symbols = tf.cast(tf.ceil(tf.cast(num_features, tf.float32) / tf.cast(bits_per_symbol, tf.float32)), tf.int32)
        
        # 填充到完整的OFDM符号
        padded_length = num_symbols * bits_per_symbol
        padded_inputs = tf.pad(inputs, [[0, 0], [0, padded_length - num_features]])
        
        # 重塑为OFDM符号格式
        ofdm_data = tf.reshape(padded_inputs, [batch_size, num_symbols, self.num_subcarriers, 2])
        
        # QPSK调制
        i_component = 2.0 * ofdm_data[:, :, :, 0] - 1.0  # I路
        q_component = 2.0 * ofdm_data[:, :, :, 1] - 1.0  # Q路
        complex_symbols = tf.complex(i_component, q_component)
        
        if training or True:
            # IFFT (OFDM调制)
            time_domain = tf.signal.ifft(complex_symbols)
            
            # 添加循环前缀
            cp = time_domain[:, :, -self.cp_length:]
            time_domain_with_cp = tf.concat([cp, time_domain], axis=-1)
            
            # 多径信道 (简化模型)
            channel_taps = tf.complex(
                tf.random.normal([batch_size, num_symbols, 1], stddev=0.5),
                tf.random.normal([batch_size, num_symbols, 1], stddev=0.5)
            )
            
            # 卷积 (简化为逐点乘法)
            received_time = time_domain_with_cp * channel_taps
            
            # 添加噪声
            snr_linear = tf.pow(10.0, snr_db / 10.0)
            signal_power = tf.reduce_mean(tf.square(tf.abs(received_time)))
            noise_power = signal_power / snr_linear
            noise_std = tf.sqrt(noise_power / 2.0)  # 复数噪声
            
            noise_real = tf.random.normal(tf.shape(received_time), stddev=noise_std)
            noise_imag = tf.random.normal(tf.shape(received_time), stddev=noise_std)
            noise = tf.complex(noise_real, noise_imag)
            
            received_time += noise
            
            # 移除循环前缀
            received_time_no_cp = received_time[:, :, self.cp_length:]
            
            # FFT (OFDM解调)
            received_freq = tf.signal.fft(received_time_no_cp)
            
            # 信道均衡 (理想估计)
            equalized = received_freq / (channel_taps + 1e-8)
        else:
            equalized = complex_symbols
        
        # QPSK解调
        i_demod = tf.real(equalized)
        q_demod = tf.imag(equalized)
        
        # 软判决
        i_bits = (i_demod + 1.0) / 2.0
        q_bits = (q_demod + 1.0) / 2.0
        
        i_bits = tf.clip_by_value(i_bits, 0.0, 1.0)
        q_bits = tf.clip_by_value(q_bits, 0.0, 1.0)
        
        # 重组比特序列
        demod_data = tf.stack([i_bits, q_bits], axis=-1)
        demod_flat = tf.reshape(demod_data, [batch_size, padded_length])
        
        # 截取到原始长度
        output = demod_flat[:, :num_features]
        
        return output

def create_channel(channel_type, **kwargs):
    """创建信道的工厂函数"""
    if channel_type.upper() == "AWGN":
        return AWGNChannel(**kwargs)
    elif channel_type.upper() == "RAYLEIGH":
        return RayleighChannel(**kwargs)
    elif channel_type.upper() == "OFDM":
        return OFDMChannel(**kwargs)
    else:
        raise ValueError(f"Unknown channel type: {channel_type}")

# 测试函数
def test_channels():
    """测试不同信道模型"""
    batch_size = 4
    features = 128
    snr_db = 10.0
    
    # 创建测试数据 (比特序列)
    test_bits = tf.random.uniform([batch_size, features], 0.0, 1.0)
    test_bits = tf.round(test_bits)  # 转换为0/1比特
    
    print(f"Input shape: {test_bits.shape}")
    print(f"Input bits (first 10): {test_bits[0, :10]}")
    
    # 测试AWGN信道
    awgn_channel = AWGNChannel()
    awgn_output = awgn_channel(test_bits, snr_db, training=True)
    print(f"\nAWGN output range: [{tf.reduce_min(awgn_output):.3f}, {tf.reduce_max(awgn_output):.3f}]")
    
    # 测试瑞利信道
    rayleigh_channel = RayleighChannel()
    rayleigh_output = rayleigh_channel(test_bits, snr_db, training=True)
    print(f"Rayleigh output range: [{tf.reduce_min(rayleigh_output):.3f}, {tf.reduce_max(rayleigh_output):.3f}]")
    
    # 测试OFDM信道
    ofdm_channel = OFDMChannel(num_subcarriers=64, cp_length=16)
    ofdm_output = ofdm_channel(test_bits, snr_db, training=True)
    print(f"OFDM output range: [{tf.reduce_min(ofdm_output):.3f}, {tf.reduce_max(ofdm_output):.3f}]")

if __name__ == "__main__":
    test_channels()
