import tensorflow as tf
from tensorflow.keras import layers
import numpy as np
from .attention_modules import MultiHeadAttention

class ImageDecoder(tf.keras.layers.Layer):
    def __init__(self, config, **kwargs):
        super(ImageDecoder, self).__init__(**kwargs)
        
        self.config = config
        self.embedding_dim = config.embedding_dim
        self.num_heads = config.TF_heads
        self.num_layers = config.dec_TF_layers
        self.compression_ratio = config.compression_ratio
        
        # 图像参数
        self.image_height = config.image_height
        self.image_width = config.image_width
        self.image_channels = config.image_channels
        
        # 压缩特征参数
        self.compressed_height = config.image_height // 8  # 32
        self.compressed_width = config.image_width // 8    # 64
        self.feature_dim = self.compressed_height * self.compressed_width  # 2048
        self.compressed_features = self.feature_dim // self.compression_ratio
        
        # 特征重建层
        self.feature_reconstruction = self._build_feature_reconstruction()
        
        # Transformer解码器层
        self.transformer_layers = self._build_transformer_layers()
        
        # CNN重建层
        self.conv_reconstruction = self._build_conv_reconstruction()
        
    def _build_feature_reconstruction(self):
        """构建特征重建层"""
        reconstruction_layers = tf.keras.Sequential([
            layers.Dense(self.embedding_dim // 2, activation='relu'),
            layers.Dropout(0.1),
            layers.Dense(self.embedding_dim, activation='relu'),
            layers.Dropout(0.1),
            layers.Dense(self.feature_dim * self.embedding_dim, activation='relu'),
            layers.Reshape([self.feature_dim, self.embedding_dim])
        ])
        return reconstruction_layers
    
    def _build_transformer_layers(self):
        """构建Transformer解码器层"""
        transformer_layers = []
        for i in range(self.num_layers):
            # 多头注意力层
            attention_layer = MultiHeadAttention(
                head_num=self.num_heads,
                name=f'decoder_attention_{i}'
            )
            
            # 前馈网络
            ffn_layer = tf.keras.Sequential([
                layers.Dense(self.embedding_dim * 4, activation='relu'),
                layers.Dropout(0.1),
                layers.Dense(self.embedding_dim),
                layers.Dropout(0.1)
            ], name=f'decoder_ffn_{i}')
            
            # 层归一化
            norm1 = layers.LayerNormalization(name=f'decoder_norm1_{i}')
            norm2 = layers.LayerNormalization(name=f'decoder_norm2_{i}')
            
            transformer_layers.append({
                'attention': attention_layer,
                'ffn': ffn_layer,
                'norm1': norm1,
                'norm2': norm2
            })
        
        return transformer_layers
    
    def _build_conv_reconstruction(self):
        """构建CNN重建层"""
        conv_layers = tf.keras.Sequential([
            # 投影到特征图 - 修复维度计算
            layers.Dense(self.compressed_height * self.compressed_width * 64),
            layers.Reshape([self.compressed_height, self.compressed_width, 64]),
            
            # 第一个反卷积块
            layers.Conv2DTranspose(128, 3, strides=1, padding='same', activation='relu'),
            layers.BatchNormalization(),
            layers.Conv2DTranspose(128, 3, strides=2, padding='same', activation='relu'),
            layers.BatchNormalization(),
            
            # 第二个反卷积块
            layers.Conv2DTranspose(128, 3, strides=1, padding='same', activation='relu'),
            layers.BatchNormalization(),
            layers.Conv2DTranspose(64, 3, strides=2, padding='same', activation='relu'),
            layers.BatchNormalization(),
            
            # 第三个反卷积块
            layers.Conv2DTranspose(64, 3, strides=1, padding='same', activation='relu'),
            layers.BatchNormalization(),
            layers.Conv2DTranspose(32, 3, strides=2, padding='same', activation='relu'),
            layers.BatchNormalization(),
            
            # 最终输出层
            layers.Conv2D(self.image_channels, 3, strides=1, padding='same', activation='sigmoid'),
        ])
        return conv_layers
    
    def _positional_encoding(self, seq_len, d_model):
        """生成正弦位置编码"""
        position = np.arange(seq_len)[:, np.newaxis]
        div_term = np.exp(np.arange(0, d_model, 2) * -(np.log(10000.0) / d_model))
        
        pos_encoding = np.zeros((seq_len, d_model))
        pos_encoding[:, 0::2] = np.sin(position * div_term)
        pos_encoding[:, 1::2] = np.cos(position * div_term)
        
        return tf.constant(pos_encoding, dtype=tf.float32)
    
    def call(self, inputs, training=False):
        """前向传播"""
        # inputs shape: [batch, compressed_features]
        batch_size = tf.shape(inputs)[0]
        
        # 特征重建
        x = self.feature_reconstruction(inputs, training=training)
        # x shape: [batch, feature_dim, embedding_dim]
        
        # 添加位置编码
        pos_encoding = self._positional_encoding(self.feature_dim, self.embedding_dim)
        x = x + pos_encoding[tf.newaxis, :, :]
        
        # Transformer解码器层
        for layer in self.transformer_layers:
            # 多头注意力
            attention_output = layer['attention'](x, training=training)
            x = layer['norm1'](x + attention_output, training=training)
            
            # 前馈网络
            ffn_output = layer['ffn'](x, training=training)
            x = layer['norm2'](x + ffn_output, training=training)
        
        # 重塑为特征图格式 - 修复维度计算
        # x shape: [batch, feature_dim, embedding_dim]
        # 我们需要将其转换为适合CNN的格式
        x = tf.reduce_mean(x, axis=1)  # [batch, embedding_dim]
        
        # CNN重建
        reconstructed_image = self.conv_reconstruction(x, training=training)
        # reconstructed_image shape: [batch, height, width, channels]
        
        return reconstructed_image
