import tensorflow as tf
from tensorflow.keras import layers
import numpy as np
from .attention_modules import MultiHeadAttention

class ImageEncoder(tf.keras.layers.Layer):
    def __init__(self, config, **kwargs):
        super(ImageEncoder, self).__init__(**kwargs)
        
        self.config = config
        self.embedding_dim = config.embedding_dim
        self.num_heads = config.TF_heads
        self.num_layers = config.enc_TF_layers
        self.compression_ratio = config.compression_ratio
        
        # 计算压缩后的特征维度
        self.compressed_height = config.image_height // 8  # 32
        self.compressed_width = config.image_width // 8    # 64
        self.feature_dim = self.compressed_height * self.compressed_width  # 2048
        
        # CNN特征提取层
        self.conv_layers = self._build_conv_layers()
        
        # 位置编码
        self.pos_embedding = self._build_positional_encoding()
        
        # Transformer编码器层
        self.transformer_layers = self._build_transformer_layers()
        
        # 最终压缩层
        self.final_compression = self._build_compression_layers()
        
    def _build_conv_layers(self):
        """构建CNN特征提取层"""
        conv_layers = tf.keras.Sequential([
            # 第一个卷积块
            layers.Conv2D(64, 3, strides=1, padding='same', activation='relu'),
            layers.BatchNormalization(),
            layers.Conv2D(64, 3, strides=2, padding='same', activation='relu'),
            layers.BatchNormalization(),
            
            # 第二个卷积块
            layers.Conv2D(128, 3, strides=1, padding='same', activation='relu'),
            layers.BatchNormalization(),
            layers.Conv2D(128, 3, strides=2, padding='same', activation='relu'),
            layers.BatchNormalization(),
            
            # 第三个卷积块
            layers.Conv2D(256, 3, strides=1, padding='same', activation='relu'),
            layers.BatchNormalization(),
            layers.Conv2D(256, 3, strides=2, padding='same', activation='relu'),
            layers.BatchNormalization(),
            
            # 投影到嵌入维度
            layers.Conv2D(self.embedding_dim, 1, strides=1, padding='same'),
        ])
        return conv_layers
    
    def _build_positional_encoding(self):
        """构建位置编码"""
        return layers.Embedding(
            input_dim=self.feature_dim,
            output_dim=self.embedding_dim
        )
    
    def _build_transformer_layers(self):
        """构建Transformer编码器层"""
        transformer_layers = []
        for i in range(self.num_layers):
            # 多头注意力层
            attention_layer = MultiHeadAttention(
                head_num=self.num_heads,
                name=f'encoder_attention_{i}'
            )
            
            # 前馈网络
            ffn_layer = tf.keras.Sequential([
                layers.Dense(self.embedding_dim * 4, activation='relu'),
                layers.Dropout(0.1),
                layers.Dense(self.embedding_dim),
                layers.Dropout(0.1)
            ], name=f'encoder_ffn_{i}')
            
            # 层归一化
            norm1 = layers.LayerNormalization(name=f'encoder_norm1_{i}')
            norm2 = layers.LayerNormalization(name=f'encoder_norm2_{i}')
            
            transformer_layers.append({
                'attention': attention_layer,
                'ffn': ffn_layer,
                'norm1': norm1,
                'norm2': norm2
            })
        
        return transformer_layers
    
    def _build_compression_layers(self):
        """构建最终压缩层"""
        # 计算压缩后的特征数量
        compressed_features = self.feature_dim // self.compression_ratio
        
        compression_layers = tf.keras.Sequential([
            layers.Dense(self.embedding_dim // 2, activation='relu'),
            layers.Dropout(0.1),
            layers.Dense(compressed_features, activation='sigmoid'),  # 输出到[0,1]用于量化
        ])
        return compression_layers
    
    def _positional_encoding(self, seq_len, d_model):
        """生成正弦位置编码"""
        position = np.arange(seq_len)[:, np.newaxis]
        div_term = np.exp(np.arange(0, d_model, 2) * -(np.log(10000.0) / d_model))
        
        pos_encoding = np.zeros((seq_len, d_model))
        pos_encoding[:, 0::2] = np.sin(position * div_term)
        pos_encoding[:, 1::2] = np.cos(position * div_term)
        
        return tf.constant(pos_encoding, dtype=tf.float32)
    
    def call(self, inputs, training=False):
        """前向传播"""
        # inputs shape: [batch, height, width, channels]
        batch_size = tf.shape(inputs)[0]
        
        # CNN特征提取
        x = self.conv_layers(inputs, training=training)
        # x shape: [batch, compressed_height, compressed_width, embedding_dim]
        
        # 重塑为序列格式
        x = tf.reshape(x, [batch_size, self.feature_dim, self.embedding_dim])
        # x shape: [batch, feature_dim, embedding_dim]
        
        # 添加位置编码
        pos_encoding = self._positional_encoding(self.feature_dim, self.embedding_dim)
        x = x + pos_encoding[tf.newaxis, :, :]
        
        # Transformer编码器层
        for layer in self.transformer_layers:
            # 多头注意力
            attention_output = layer['attention'](x, training=training)
            x = layer['norm1'](x + attention_output, training=training)
            
            # 前馈网络
            ffn_output = layer['ffn'](x, training=training)
            x = layer['norm2'](x + ffn_output, training=training)
        
        # 全局平均池化
        x = tf.reduce_mean(x, axis=1)  # [batch, embedding_dim]
        
        # 最终压缩
        compressed_features = self.final_compression(x, training=training)
        # compressed_features shape: [batch, compressed_features]
        
        return compressed_features
