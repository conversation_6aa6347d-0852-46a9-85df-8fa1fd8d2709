#!/usr/bin/env python3
"""
测试Image JSCC系统的各个组件
"""

import os
import sys
import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt

# 设置GPU
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
gpus = tf.config.list_physical_devices('GPU')
if gpus:
    try:
        tf.config.experimental.set_memory_growth(gpus[0], True)
    except RuntimeError as e:
        print(e)

# 导入自定义模块
from config import ImageJSCCConfig
from data.image_dataset import get_image_dataset, test_dataset
from model.image_jscc_model import create_model, test_model
from loss.image_loss import test_losses
from layers.quantization import test_quantization
from channel.channel import test_channels

def test_data_loading():
    """测试数据加载"""
    print("=" * 60)
    print("Testing Data Loading")
    print("=" * 60)
    
    try:
        config = ImageJSCCConfig()
        print(f"Data root: {config.data_root}")
        
        # 检查数据目录是否存在
        if not os.path.exists(config.data_root):
            print(f"Warning: Data directory {config.data_root} does not exist!")
            return False
        
        # 测试数据集创建
        test_dataset(config)
        
        # 创建数据集并检查一个批次
        dataset = get_image_dataset(config, batch_size=2, dataset_type='train')
        
        for batch in dataset.take(1):
            print(f"Successfully loaded batch with shape: {batch.shape}")
            print(f"Data type: {batch.dtype}")
            print(f"Value range: [{tf.reduce_min(batch):.3f}, {tf.reduce_max(batch):.3f}]")
            
            # 保存一个样本图像
            sample_dir = "Image_JSCC/test_outputs"
            os.makedirs(sample_dir, exist_ok=True)
            
            plt.figure(figsize=(10, 5))
            for i in range(min(2, batch.shape[0])):
                plt.subplot(1, 2, i+1)
                plt.imshow(batch[i].numpy())
                plt.title(f'Sample {i+1}')
                plt.axis('off')
            
            plt.tight_layout()
            plt.savefig(os.path.join(sample_dir, 'data_samples.png'), dpi=150, bbox_inches='tight')
            plt.close()
            print(f"Sample images saved to {sample_dir}/data_samples.png")
            break
        
        print("✓ Data loading test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Data loading test failed: {e}")
        return False

def test_model_creation():
    """测试模型创建"""
    print("\n" + "=" * 60)
    print("Testing Model Creation")
    print("=" * 60)
    
    try:
        config = ImageJSCCConfig()
        
        # 测试模型创建
        print("Creating model...")
        model = create_model(config)
        
        # 显示模型摘要
        model.summary_custom()
        
        # 测试前向传播
        print("\nTesting forward pass...")
        test_input = tf.random.uniform([2, config.image_height, config.image_width, config.image_channels])
        
        # 编码
        encoded = model.encode(test_input)
        print(f"Encoded shape: {encoded.shape}")
        print(f"Encoded range: [{tf.reduce_min(encoded):.3f}, {tf.reduce_max(encoded):.3f}]")
        
        # 完整前向传播
        output = model(test_input, snr_db=config.snr_dB, training=False)
        print(f"Output shape: {output.shape}")
        print(f"Output range: [{tf.reduce_min(output):.3f}, {tf.reduce_max(output):.3f}]")
        
        # 计算压缩比
        compression_ratio = model.get_compression_ratio()
        print(f"Compression ratio: {compression_ratio:.2f}")
        
        print("✓ Model creation test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Model creation test failed: {e}")
        return False

def test_end_to_end():
    """测试端到端流程"""
    print("\n" + "=" * 60)
    print("Testing End-to-End Pipeline")
    print("=" * 60)
    
    try:
        config = ImageJSCCConfig()
        
        # 创建模型
        model = create_model(config)
        
        # 创建数据集
        dataset = get_image_dataset(config, batch_size=4, dataset_type='train')
        
        # 获取一个批次
        for x_batch in dataset.take(1):
            print(f"Input batch shape: {x_batch.shape}")
            
            # 测试不同SNR下的性能
            snr_values = [0, 5, 10, 15, 20]
            results = []
            
            for snr in snr_values:
                # 前向传播
                y_pred = model(x_batch, snr_db=snr, training=False)
                
                # 计算MSE
                mse = tf.reduce_mean(tf.square(x_batch - y_pred))
                
                # 计算PSNR
                psnr = tf.image.psnr(x_batch, y_pred, max_val=1.0)
                psnr_mean = tf.reduce_mean(psnr)
                
                results.append({
                    'snr': snr,
                    'mse': mse.numpy(),
                    'psnr': psnr_mean.numpy()
                })
                
                print(f"SNR {snr:2d} dB: MSE={mse:.6f}, PSNR={psnr_mean:.2f} dB")
            
            # 保存结果图像
            sample_dir = "Image_JSCC/test_outputs"
            os.makedirs(sample_dir, exist_ok=True)
            
            # 选择中等SNR进行可视化
            y_pred_vis = model(x_batch[:4], snr_db=10, training=False)
            
            fig, axes = plt.subplots(2, 4, figsize=(16, 8))
            for i in range(4):
                # 原始图像
                axes[0, i].imshow(x_batch[i].numpy())
                axes[0, i].set_title(f'Original {i+1}')
                axes[0, i].axis('off')
                
                # 重建图像
                axes[1, i].imshow(y_pred_vis[i].numpy())
                axes[1, i].set_title(f'Reconstructed {i+1}')
                axes[1, i].axis('off')
            
            plt.suptitle('End-to-End Test: Original vs Reconstructed (SNR=10dB)')
            plt.tight_layout()
            plt.savefig(os.path.join(sample_dir, 'end_to_end_test.png'), dpi=150, bbox_inches='tight')
            plt.close()
            
            # 绘制SNR vs 性能曲线
            snrs = [r['snr'] for r in results]
            psnrs = [r['psnr'] for r in results]
            mses = [r['mse'] for r in results]
            
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
            
            ax1.plot(snrs, psnrs, 'b-o')
            ax1.set_xlabel('SNR (dB)')
            ax1.set_ylabel('PSNR (dB)')
            ax1.set_title('PSNR vs SNR')
            ax1.grid(True)
            
            ax2.semilogy(snrs, mses, 'r-o')
            ax2.set_xlabel('SNR (dB)')
            ax2.set_ylabel('MSE')
            ax2.set_title('MSE vs SNR')
            ax2.grid(True)
            
            plt.tight_layout()
            plt.savefig(os.path.join(sample_dir, 'performance_curves.png'), dpi=150, bbox_inches='tight')
            plt.close()
            
            print(f"Results saved to {sample_dir}/")
            break
        
        print("✓ End-to-end test passed!")
        return True
        
    except Exception as e:
        print(f"✗ End-to-end test failed: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("Starting Image JSCC System Tests...")
    print("=" * 60)
    
    test_results = []
    
    # 测试各个组件
    tests = [
        ("Quantization", test_quantization),
        ("Channel Models", test_channels),
        ("Loss Functions", test_losses),
        ("Data Loading", test_data_loading),
        ("Model Creation", test_model_creation),
        ("End-to-End", test_end_to_end),
    ]
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} test failed with exception: {e}")
            test_results.append((test_name, False))
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("Test Summary")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ PASSED" if result else "✗ FAILED"
        print(f"{test_name:20s}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The system is ready for training.")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
