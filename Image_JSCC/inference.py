#!/usr/bin/env python3
"""
Image JSCC推理脚本
用于加载训练好的模型并进行图像传输测试
"""

import os
import sys
import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import argparse

# 设置GPU
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
gpus = tf.config.list_physical_devices('GPU')
if gpus:
    try:
        tf.config.experimental.set_memory_growth(gpus[0], True)
    except RuntimeError as e:
        print(e)

# 导入自定义模块
from config import ImageJSCCConfig
from model.image_jscc_model import create_model
from loss.image_loss import ImageMetrics

class ImageJSCCInference:
    """Image JSCC推理类"""
    
    def __init__(self, config, model_path):
        self.config = config
        self.model = create_model(config)
        self.metrics = ImageMetrics()
        
        # 加载模型权重
        if os.path.exists(model_path):
            self.model.load_weights_custom(model_path)
            print(f"Model loaded from: {model_path}")
        else:
            print(f"Warning: Model path {model_path} not found. Using random weights.")
    
    def preprocess_image(self, image_path):
        """预处理单张图像"""
        # 读取图像
        img = Image.open(image_path).convert('RGB')
        
        # 调整大小
        img = img.resize((self.config.image_width, self.config.image_height))
        
        # 转换为numpy数组并归一化
        img_array = np.array(img, dtype=np.float32) / 255.0
        
        # 添加batch维度
        img_batch = np.expand_dims(img_array, axis=0)
        
        return tf.constant(img_batch)
    
    def transmit_image(self, image_tensor, snr_db):
        """通过JSCC系统传输图像"""
        # 前向传播
        reconstructed = self.model(image_tensor, snr_db=snr_db, training=False)
        
        # 计算指标
        metrics = self.metrics(image_tensor, reconstructed)
        
        return reconstructed, metrics
    
    def save_comparison(self, original, reconstructed, save_path, snr_db, metrics):
        """保存原始图像和重建图像的对比"""
        fig, axes = plt.subplots(1, 2, figsize=(12, 6))
        
        # 原始图像
        axes[0].imshow(original[0].numpy())
        axes[0].set_title('Original Image')
        axes[0].axis('off')
        
        # 重建图像
        axes[1].imshow(reconstructed[0].numpy())
        axes[1].set_title(f'Reconstructed (SNR={snr_db}dB)\n'
                         f'PSNR: {metrics["psnr"]:.2f}dB, SSIM: {metrics["ssim"]:.4f}')
        axes[1].axis('off')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"Comparison saved to: {save_path}")
    
    def test_snr_range(self, image_tensor, snr_range, save_dir):
        """测试不同SNR下的性能"""
        results = []
        
        os.makedirs(save_dir, exist_ok=True)
        
        print(f"Testing SNR range: {snr_range}")
        print("-" * 50)
        
        for snr in snr_range:
            # 传输图像
            reconstructed, metrics = self.transmit_image(image_tensor, snr)
            
            # 记录结果
            result = {
                'snr': snr,
                'mse': float(metrics['mse']),
                'psnr': float(metrics['psnr']),
                'ssim': float(metrics['ssim']),
                'mae': float(metrics['mae'])
            }
            results.append(result)
            
            print(f"SNR {snr:2d}dB: PSNR={result['psnr']:5.2f}dB, "
                  f"SSIM={result['ssim']:.4f}, MSE={result['mse']:.6f}")
            
            # 保存对比图像
            comparison_path = os.path.join(save_dir, f'comparison_snr_{snr}db.png')
            self.save_comparison(image_tensor, reconstructed, comparison_path, snr, metrics)
        
        # 绘制性能曲线
        self.plot_performance_curves(results, save_dir)
        
        return results
    
    def plot_performance_curves(self, results, save_dir):
        """绘制性能曲线"""
        snrs = [r['snr'] for r in results]
        psnrs = [r['psnr'] for r in results]
        ssims = [r['ssim'] for r in results]
        mses = [r['mse'] for r in results]
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # PSNR vs SNR
        axes[0, 0].plot(snrs, psnrs, 'b-o', linewidth=2, markersize=6)
        axes[0, 0].set_xlabel('SNR (dB)')
        axes[0, 0].set_ylabel('PSNR (dB)')
        axes[0, 0].set_title('PSNR vs SNR')
        axes[0, 0].grid(True, alpha=0.3)
        
        # SSIM vs SNR
        axes[0, 1].plot(snrs, ssims, 'g-o', linewidth=2, markersize=6)
        axes[0, 1].set_xlabel('SNR (dB)')
        axes[0, 1].set_ylabel('SSIM')
        axes[0, 1].set_title('SSIM vs SNR')
        axes[0, 1].grid(True, alpha=0.3)
        
        # MSE vs SNR (log scale)
        axes[1, 0].semilogy(snrs, mses, 'r-o', linewidth=2, markersize=6)
        axes[1, 0].set_xlabel('SNR (dB)')
        axes[1, 0].set_ylabel('MSE')
        axes[1, 0].set_title('MSE vs SNR (log scale)')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 综合性能对比
        axes[1, 1].plot(snrs, np.array(psnrs)/max(psnrs), 'b-o', label='PSNR (normalized)', linewidth=2)
        axes[1, 1].plot(snrs, ssims, 'g-o', label='SSIM', linewidth=2)
        axes[1, 1].set_xlabel('SNR (dB)')
        axes[1, 1].set_ylabel('Normalized Score')
        axes[1, 1].set_title('Performance Comparison')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'performance_curves.png'), dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"Performance curves saved to: {save_dir}/performance_curves.png")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Image JSCC Inference')
    parser.add_argument('--image', type=str, required=True, help='Input image path')
    parser.add_argument('--model', type=str, help='Model checkpoint path')
    parser.add_argument('--snr', type=int, default=10, help='SNR in dB (for single test)')
    parser.add_argument('--snr_range', nargs=2, type=int, default=[0, 20], 
                       help='SNR range for testing (min max)')
    parser.add_argument('--output', type=str, default='inference_results', 
                       help='Output directory')
    parser.add_argument('--test_range', action='store_true', 
                       help='Test over SNR range instead of single SNR')
    
    args = parser.parse_args()
    
    # 检查输入图像
    if not os.path.exists(args.image):
        print(f"Error: Image file {args.image} not found!")
        return
    
    # 加载配置
    config = ImageJSCCConfig()
    
    # 确定模型路径
    if args.model:
        model_path = args.model
    else:
        # 使用默认的最佳模型路径
        model_path = os.path.join(config.ckpt_dir, 'best_model')
    
    # 创建推理器
    inference = ImageJSCCInference(config, model_path)
    
    # 预处理图像
    print(f"Loading image: {args.image}")
    image_tensor = inference.preprocess_image(args.image)
    print(f"Image shape: {image_tensor.shape}")
    
    # 创建输出目录
    os.makedirs(args.output, exist_ok=True)
    
    if args.test_range:
        # 测试SNR范围
        snr_range = list(range(args.snr_range[0], args.snr_range[1] + 1, 2))
        results = inference.test_snr_range(image_tensor, snr_range, args.output)
        
        # 保存结果到CSV
        import csv
        csv_path = os.path.join(args.output, 'results.csv')
        with open(csv_path, 'w', newline='') as csvfile:
            fieldnames = ['snr', 'mse', 'psnr', 'ssim', 'mae']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for result in results:
                writer.writerow(result)
        print(f"Results saved to: {csv_path}")
        
    else:
        # 单个SNR测试
        print(f"Testing at SNR = {args.snr} dB")
        reconstructed, metrics = inference.transmit_image(image_tensor, args.snr)
        
        # 保存对比图像
        comparison_path = os.path.join(args.output, f'comparison_snr_{args.snr}db.png')
        inference.save_comparison(image_tensor, reconstructed, comparison_path, args.snr, metrics)
        
        # 打印结果
        print(f"Results:")
        print(f"  PSNR: {metrics['psnr']:.2f} dB")
        print(f"  SSIM: {metrics['ssim']:.4f}")
        print(f"  MSE:  {metrics['mse']:.6f}")
        print(f"  MAE:  {metrics['mae']:.6f}")

if __name__ == "__main__":
    main()
